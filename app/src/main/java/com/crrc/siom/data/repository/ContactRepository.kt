package com.crrc.siom.data.repository

import android.annotation.SuppressLint
import androidx.annotation.CheckResult
import com.crrc.common.BaseResponse
import com.crrc.common.bean.response.AddressBookListResponse
import com.crrc.common.bean.response.ChatInfo
import com.crrc.common.bean.response.UploadResponse
import com.crrc.network.NetworkApi
import com.crrc.network.api.ApiInterface
import com.crrc.network.errorhandler.ExceptionHandle
import com.crrc.network.observer.BaseObserverForBasic
import com.crrc.siom.data.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import android.util.Log
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.File

interface ContactRepository {
    suspend fun searchContacts(query: String): List<Contact>
    fun getAddressBookList(userId: String, callback: (AddressBookListResponse?, String?) -> Unit)
    fun getConversation(type: Int, initiatorUserId: String, targetId: String, callback: (String?, String?) -> Unit)
    fun getChatList(userId: String, callback: (List<ChatInfo>?, String?) -> Unit)
    fun upload(filePath: String, callback: (List<UploadResponse>?, String?) -> Unit)
    fun uploadMultiple(filePaths: List<String>, callback: (List<UploadResponse>?, String?) -> Unit)
}

@SuppressLint("CheckResult")
class ContactRepositoryImpl : ContactRepository {
    override suspend fun searchContacts(query: String): List<Contact> =
        withContext(Dispatchers.IO) {
            // TODO: 实现搜索功能
            emptyList()
        }

    override fun getAddressBookList(
        userId: String,
        callback: (AddressBookListResponse?, String?) -> Unit
    ) {
        NetworkApi.getService(ApiInterface::class.java)
            .getAddressBookList(userId)
            .compose(
                NetworkApi.getInstance().applySchedulers(object :
                    BaseObserverForBasic<BaseResponse<AddressBookListResponse>>() {
                    override fun onSuccess(response: BaseResponse<AddressBookListResponse>) {
                        callback(response.data, null)
                    }

                    override fun onFail(e: Throwable) {
                        callback(null, e.message)
                    }
                })
            )
    }

    override fun getConversation(
        type: Int,
        initiatorUserId: String,
        targetId: String,
        callback: (String?, String?) -> Unit
    ) {
        NetworkApi.getService(ApiInterface::class.java)
            .getConversation(type, initiatorUserId, targetId)
            .compose(
                NetworkApi.getInstance()
                    .applySchedulers(object : BaseObserverForBasic<BaseResponse<String>>() {
                        override fun onSuccess(response: BaseResponse<String>) {
                            callback(response.data, null)
                        }

                        override fun onFail(e: Throwable) {
                            callback(null, e.message)
                        }
                    })
            )
    }

    @Deprecated("由websocket接口 PARTICIPANT_LIST_UPDATE 替代")
    override fun getChatList(
        userId: String,
        callback: (List<ChatInfo>?, String?) -> Unit
    ) {
        NetworkApi.getService(ApiInterface::class.java)
            .getChatList(userId)
            .compose(
                NetworkApi.getInstance()
                    .applySchedulers(object : BaseObserverForBasic<BaseResponse<List<ChatInfo>>>() {
                        override fun onSuccess(response: BaseResponse<List<ChatInfo>>) {
                            callback(response.data, null)
                        }
                        override fun onFail(e: Throwable) {
                            callback(null, e.message)
                        }
                    })
            )
    }
    override fun upload(
        filePath: String,
        callback: (List<UploadResponse>?, String?) -> Unit
    ) {
        // 单文件上传，调用多文件上传方法
        uploadMultiple(listOf(filePath), callback)
    }

    override fun uploadMultiple(
        filePaths: List<String>,
        callback: (List<UploadResponse>?, String?) -> Unit
    ) {
        try {
            if (filePaths.isEmpty()) {
                callback(null, "没有选择文件")
                return
            }

            Log.d("ContactRepository", "开始上传文件，文件数量: ${filePaths.size}")
            val fileParts = mutableListOf<MultipartBody.Part>()

            for (filePath in filePaths) {
                val file = File(filePath)
                Log.d("ContactRepository", "处理文件: $filePath, 存在: ${file.exists()}, 大小: ${file.length()}")
                if (!file.exists()) {
                    callback(null, "文件不存在: $filePath")
                    return
                }
                val requestFile = file.asRequestBody("*/*".toMediaTypeOrNull())
                val filePart = MultipartBody.Part.createFormData("file", file.name, requestFile)
                fileParts.add(filePart)
                Log.d("ContactRepository", "添加文件到上传列表: ${file.name}")
            }

            Log.d("ContactRepository", "准备上传，文件部分数量: ${fileParts.size}")

            NetworkApi.getService(ApiInterface::class.java)
                .upload(fileParts)
                .compose(
                    NetworkApi.getInstance().applySchedulers(object :
                        BaseObserverForBasic<BaseResponse<List<UploadResponse>>>() {
                        override fun onSuccess(response: BaseResponse<List<UploadResponse>>) {
                            callback(response.data, null)
                        }

                        override fun onFail(e: Throwable) {
                            callback(null, e.message)
                        }
                    })
                )
        } catch (e: Exception) {
            callback(null, e.message ?: "上传文件失败")
        }
    }
}