package com.crrc.siom.ui.dashboard.detail

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.crrc.common.Constant.EXPERT_GROUP_TYPE
import com.crrc.common.Constant.GROUP_TYPE
import com.crrc.common.utils.ToastUtil
import com.crrc.siom.data.SessionManager
import com.crrc.siom.data.model.ChatInfo
import com.crrc.siom.data.model.Message
import com.crrc.siom.data.model.ChatMessage
import com.crrc.siom.data.model.GroupChatMessage
import com.crrc.siom.data.model.HistoryMessage
import com.crrc.siom.data.model.HistoryMessageResponse
import com.crrc.siom.data.model.WsMessage
import com.crrc.siom.data.repository.ContactRepository
import com.crrc.siom.data.repository.ContactRepositoryImpl
import com.crrc.common.bean.response.UploadResponse
import com.crrc.siom.service.WebSocketService
import com.crrc.siom.manager.ChatMessageCenter
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
class ChatDetailViewModel : ViewModel() {
    private val repository: ContactRepository = ContactRepositoryImpl()

    private val _conversationId = MutableStateFlow<String>("")
    val conversationId: StateFlow<String> = _conversationId

    private val _chatMessages = MutableStateFlow<List<WsMessage>>(emptyList())
    val chatMessages: StateFlow<List<WsMessage>> = _chatMessages

    private var chatBinder: WebSocketService.ChatBinder? = null

    // 分页相关字段
    private var lastHistoryMessageId: String? = null
    var isLoadingHistory = false
        private set
    var hasMoreHistory = true
        private set

    // 文件上传状态
    private val _isUploadingFile = MutableStateFlow(false)
    val isUploadingFile: StateFlow<Boolean> = _isUploadingFile

    init {
        viewModelScope.launch {
            ChatMessageCenter.newMessageFlow.collect { message ->
                when(message){
                    is ChatMessage, is GroupChatMessage -> {
                        _chatMessages.value = listOf(message) + _chatMessages.value
                    }
                    is HistoryMessageResponse -> {
                        val historyList = message.messageList
                        if (historyList.isNotEmpty()) {
                            lastHistoryMessageId = historyList.first().id
                            _chatMessages.value = _chatMessages.value + message
                            hasMoreHistory = message.hasMore
                        } else {
                            hasMoreHistory = false
                        }
                        isLoadingHistory = false
                    }
                    else -> {
                        // 处理其他类型的消息
                    }
                }
            }
        }
    }

    private fun tryLoadHistoryIfReady() {
        if (chatBinder != null && _conversationId.value.isNotBlank() && _chatMessages.value.isEmpty()) {
            getHistoryMessage()
        }
    }

    fun setChatBinder(binder: WebSocketService.ChatBinder?) {
        chatBinder = binder
        tryLoadHistoryIfReady()
    }

    fun setConversationId(id: String) {
        _conversationId.value = id
        tryLoadHistoryIfReady()
    }

    // 发送消息
    fun sendMessage(content: String,chatType: Int) {
        if (content.isBlank()) return
        val conversationId = _conversationId.value
        val senderId = SessionManager().getUserId().toString()
        val replyTo: String? = null

        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val time = sdf.format(Date())
        val messageList = listOf(
            Message(
                type = "text",
                text = content,
                id = "",
                conversationId = conversationId,
                senderId = senderId,
                senderName = "",
                attachments = "",
                time = time,
                isRecalled = "",
                replyTo = replyTo.toString(),
            )
        )
        val chatMessageType = when (chatType) {
            0 -> "SINGLE_SENDING"
            else -> "GROUP_SENDING"    //1,2
        }

        val chatMessage = ChatMessage(
            type = chatMessageType,
            conversationId = conversationId,
            messageList = messageList,
        )
        chatBinder?.sendSingleMessage(chatMessage)
        _chatMessages.value = listOf(chatMessage) + _chatMessages.value
    }

    fun getHistoryMessage(){
        if (isLoadingHistory || !hasMoreHistory) return
        isLoadingHistory = true

        // 添加超时重置机制
        viewModelScope.launch {
            kotlinx.coroutines.delay(10000) // 10秒超时
            if (isLoadingHistory) {
                Log.w("ChatDetailViewModel", "历史消息加载超时，重置状态")
                isLoadingHistory = false
            }
        }

        chatBinder?.sendHistoryMessage(
            HistoryMessage(
                type = "HISTORY_MESSAGE",
                userId = SessionManager().getUserId().toString(),
                conversationId = _conversationId.value,
                limit = 14,
                lastMessageId = lastHistoryMessageId
            )
        )
    }

    fun getConversation(type: Int, initiatorUserId: String, targetId: String, ) {
        if (type == GROUP_TYPE || type == EXPERT_GROUP_TYPE){
            Log.d("type",targetId.toString())
            setConversationId(targetId)
            return
        }
        repository.getConversation(type, initiatorUserId, targetId) { conversationId, error ->
            if (error != null) {
                ToastUtil.show(error)
            } else {
                setConversationId(conversationId.toString())
            }
        }
    }

    /**
     * 上传文件并发送文件消息
     * @param filePath 文件路径
     * @param chatType 聊天类型 (0: 单聊, 1: 群聊)
     */
    fun uploadAndSendFile(filePath: String, chatType: Int) {
        if (filePath.isBlank()) return

        // 防止重复上传
        if (_isUploadingFile.value) {
            Log.d("ChatDetailViewModel", "文件正在上传中，忽略重复请求")
            return
        }

        Log.d("ChatDetailViewModel", "开始上传文件: $filePath")
        _isUploadingFile.value = true

        repository.upload(filePath) { uploadResponses, error ->
            try {
                Log.d("ChatDetailViewModel", "文件上传回调触发，error: $error, responses: ${uploadResponses?.records?.size}")

                if (error != null) {
                    Log.e("ChatDetailViewModel", "文件上传失败: $error")
                    ToastUtil.show("文件上传失败: $error")
                    _isUploadingFile.value = false
                    return@upload
                }

                if (uploadResponses?.records.isNullOrEmpty()) {
                    Log.e("ChatDetailViewModel", "服务器未返回文件信息")
                    ToastUtil.show("文件上传失败: 服务器未返回文件信息")
                    _isUploadingFile.value = false
                    return@upload
                }

                Log.d("ChatDetailViewModel", "文件上传成功，收到${uploadResponses.records.size}个文件响应")

                // 上传成功，发送文件消息
                val uploadResponse = uploadResponses.records.first()
                val fileName = uploadResponse.name ?: "未知文件"
                val fileUrl = uploadResponse.url ?: ""
                val fileId = uploadResponse.id ?: ""
                val fileSize = uploadResponse.size ?: "0"
                val fileType = uploadResponse.type ?: ""

                // 构建文件消息内容
                val fileInfo = mapOf(
                    "id" to fileId,
                    "name" to fileName,
                    "url" to fileUrl,
                    "size" to fileSize,
                    "type" to fileType
                )

                // 将文件信息转为JSON字符串
                val fileInfoJson = com.google.gson.Gson().toJson(fileInfo)

                // 发送文件消息
                sendFileMessage(fileName, fileInfoJson, chatType)
                Log.d("ChatDetailViewModel", "文件消息发送完成")

            } catch (e: Exception) {
                Log.e("ChatDetailViewModel", "处理上传响应时出错", e)
                ToastUtil.show("处理上传结果失败: ${e.message}")
            } finally {
                // 确保无论如何都重置上传状态
                _isUploadingFile.value = false
                Log.d("ChatDetailViewModel", "上传状态已重置为false")
            }
        }
    }

    /**
     * 发送文件消息
     * @param fileName 文件名
     * @param fileInfo 文件信息JSON字符串
     * @param chatType 聊天类型
     */
    private fun sendFileMessage(fileName: String, fileInfo: String, chatType: Int) {
        val conversationId = _conversationId.value
        val senderId = SessionManager().getUserId().toString()
        val replyTo: String? = null

        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val time = sdf.format(Date())

        val messageList = listOf(
            Message(
                type = "file",  // 消息类型为文件
                text = fileName, // 文件名作为显示文本
                id = "",
                conversationId = conversationId,
                senderId = senderId,
                senderName = "",
                attachments = fileInfo, // 文件信息作为附件
                time = time,
                isRecalled = "",
                replyTo = replyTo.toString(),
            )
        )

        val chatMessageType = when (chatType) {
            0 -> "SINGLE_SENDING"
            else -> "GROUP_SENDING"    //1,2
        }

        val chatMessage = ChatMessage(
            type = chatMessageType,
            conversationId = conversationId,
            messageList = messageList,
        )

        chatBinder?.sendSingleMessage(chatMessage)
        _chatMessages.value = listOf(chatMessage) + _chatMessages.value
    }
}